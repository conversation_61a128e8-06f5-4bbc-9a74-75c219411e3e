[{"timestamp": "2025-08-03T09:34:24.918395", "test_name": "SSE连接测试", "success": false, "details": "状态码: 400", "error": "{\"error\":\"Invalid or missing MCP session ID for SSE.\"}", "session_id": "d3a11753-66a8-41cb-ae57-62f23f9485dc"}, {"timestamp": "2025-08-03T09:34:27.023493", "test_name": "MCP调用测试 1 (initialize)", "success": false, "details": "状态码: 406", "error": "{\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32000,\"message\":\"Not Acceptable: Client must accept both application/json and text/event-stream\"},\"id\":null}", "session_id": "d3a11753-66a8-41cb-ae57-62f23f9485dc"}, {"timestamp": "2025-08-03T09:34:28.057867", "test_name": "MCP调用测试 2 (tools/list)", "success": false, "details": "状态码: 400", "error": "{\"error\":\"Invalid MCP request or session.\"}", "session_id": "d3a11753-66a8-41cb-ae57-62f23f9485dc"}, {"timestamp": "2025-08-03T09:34:31.079199", "test_name": "直接工具调用测试", "success": false, "details": "状态码: 400", "error": "{\"error\":\"Invalid MCP request or session.\"}", "session_id": "d3a11753-66a8-41cb-ae57-62f23f9485dc"}]