#!/usr/bin/env python3
"""
Chrome MCP Server 连接诊断测试脚本
用于诊断和测试Chrome MCP Server的连接状态
"""

import requests
import json
import time
import sys
from datetime import datetime

class ChromeMCPTester:
    def __init__(self):
        self.base_url = "http://127.0.0.1:12306/mcp"
        self.test_results = []
        
    def log_test(self, test_name, success, details="", error=""):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error
        }
        self.test_results.append(result)
        
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def test_basic_connection(self):
        """测试基础连接"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                self.log_test("基础连接测试", True, f"状态码: {response.status_code}")
                return True
            else:
                self.log_test("基础连接测试", False, f"状态码: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError as e:
            self.log_test("基础连接测试", False, error=f"连接错误: {str(e)}")
            return False
        except Exception as e:
            self.log_test("基础连接测试", False, error=f"未知错误: {str(e)}")
            return False
    
    def test_mcp_endpoint(self):
        """测试MCP端点"""
        try:
            # 尝试发送一个简单的MCP请求
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list",
                "params": {}
            }
            
            response = requests.post(
                self.base_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "result" in data:
                    tools_count = len(data.get("result", {}).get("tools", []))
                    self.log_test("MCP端点测试", True, f"发现 {tools_count} 个工具")
                    return True
                else:
                    self.log_test("MCP端点测试", False, f"响应格式异常: {data}")
                    return False
            else:
                self.log_test("MCP端点测试", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("MCP端点测试", False, error=str(e))
            return False
    
    def test_chrome_extension_status(self):
        """检查Chrome扩展状态"""
        try:
            # 尝试获取窗口和标签页信息
            payload = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "get_windows_and_tabs",
                    "arguments": {}
                }
            }
            
            response = requests.post(
                self.base_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "result" in data and not data.get("error"):
                    self.log_test("Chrome扩展状态测试", True, "扩展正常工作")
                    return True
                else:
                    error_msg = data.get("error", {}).get("message", "未知错误")
                    self.log_test("Chrome扩展状态测试", False, error=error_msg)
                    return False
            else:
                self.log_test("Chrome扩展状态测试", False, f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Chrome扩展状态测试", False, error=str(e))
            return False
    
    def run_all_tests(self):
        """运行所有诊断测试"""
        print("🔍 开始Chrome MCP Server连接诊断...")
        print("=" * 50)
        
        # 测试序列
        tests = [
            self.test_basic_connection,
            self.test_mcp_endpoint,
            self.test_chrome_extension_status
        ]
        
        success_count = 0
        for test in tests:
            if test():
                success_count += 1
            time.sleep(1)  # 测试间隔
        
        print("=" * 50)
        print(f"📊 测试完成: {success_count}/{len(tests)} 项测试通过")
        
        if success_count == len(tests):
            print("🎉 所有测试通过！Chrome MCP Server连接正常")
            return True
        else:
            print("⚠️  部分测试失败，请检查配置")
            return False
    
    def save_results(self, filename="test-doc/chrome-mcp-connection-results.json"):
        """保存测试结果到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            print(f"📄 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

def main():
    """主函数"""
    tester = ChromeMCPTester()
    
    print("Chrome MCP Server 连接诊断工具")
    print("=" * 50)
    
    success = tester.run_all_tests()
    tester.save_results()
    
    if not success:
        print("\n🔧 故障排除建议:")
        print("1. 确认Chrome浏览器正在运行")
        print("2. 确认Chrome MCP扩展已安装并启用")
        print("3. 确认MCP server正在端口12306上运行")
        print("4. 检查防火墙设置")
        print("5. 尝试重启Chrome浏览器和MCP server")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
