#!/usr/bin/env python3
"""
WSL截屏工具 - 专用于WSL环境，直接调用Windows截屏程序
文件名: wsl_screenshot.py
环境要求: WSL环境，能够调用Windows程序
功能: 无需PowerShell，直接从WSL调用Windows程序进行截屏
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from pathlib import Path

def check_wsl_environment():
    """检查是否在WSL环境中"""
    try:
        with open('/proc/version', 'r') as f:
            return 'microsoft' in f.read().lower()
    except FileNotFoundError:
        return False

def take_screenshot_wsl(delay=0, output_file=None):
    """
    在WSL中通过调用Windows程序截屏
    """
    if not check_wsl_environment():
        print("❌ 此工具需要在WSL环境中运行")
        return False
    
    if delay > 0:
        print(f"⏰ 等待 {delay} 秒后截屏...")
        time.sleep(delay)
    
    # 生成输出文件名
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"screenshot_{timestamp}.png"
    
    # 确保输出文件使用绝对路径，并保存到Test-Image目录
    if not os.path.isabs(output_file):
        script_dir = Path(__file__).parent.parent  # 回到项目根目录
        test_image_dir = script_dir / "Test-Image"
        test_image_dir.mkdir(exist_ok=True)  # 确保目录存在
        output_file = test_image_dir / output_file
    
    # 转换为Windows路径
    try:
        # 使用wslpath转换路径
        windows_path = subprocess.check_output(
            ['wslpath', '-w', str(output_file)], 
            text=True
        ).strip()
        
        print(f"💾 保存到: {windows_path}")
        
        # 方法1: 使用PowerShell的Screenshot功能
        ps_command = f"""
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing
$bounds = [System.Windows.Forms.SystemInformation]::VirtualScreen
$bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)
$graphics.CopyFromScreen($bounds.Location, [System.Drawing.Point]::Empty, $bounds.Size)
$bitmap.Save('{windows_path}', [System.Drawing.Imaging.ImageFormat]::Png)
$bitmap.Dispose()
$graphics.Dispose()
Write-Host '✅ 截屏已保存到: {windows_path}'
"""
        
        # 执行PowerShell命令
        result = subprocess.run([
            'powershell.exe', '-Command', ps_command
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 截屏成功: {output_file}")
            return str(output_file)
        else:
            print(f"❌ PowerShell截屏失败: {result.stderr}")
            
            # 方法2: 使用Windows内置的截屏工具
            print("🔄 尝试使用备选方案...")
            return take_screenshot_alternative(windows_path)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 路径转换失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 截屏过程出错: {e}")
        return False

def take_screenshot_alternative(windows_path):
    """备选截屏方案"""
    try:
        # 使用Windows的Snippin Tool或其他截屏工具
        # 这里可以扩展更多备选方案
        print("🔧 正在尝试备选截屏方案...")
        
        # 简单的cmd命令截屏（如果系统支持）
        cmd_command = f'''
powershell -Command "& {{
    Add-Type -AssemblyName System.Windows.Forms,System.Drawing
    $bounds = [Drawing.Rectangle]::FromLTRB(0, 0, 1920, 1080)
    $bmp = New-Object Drawing.Bitmap $bounds.width, $bounds.height
    $graphics = [Drawing.Graphics]::FromImage($bmp)
    $graphics.CopyFromScreen($bounds.Location, [Drawing.Point]::Empty, $bounds.size)
    $bmp.Save('{windows_path}')
    $bmp.Dispose()
    $graphics.Dispose()
}}"
'''
        
        result = subprocess.run(['cmd.exe', '/c', cmd_command], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 备选方案截屏成功")
            return windows_path
        else:
            print(f"❌ 备选方案也失败了: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 备选方案执行出错: {e}")
        return False

def main():
    import argparse
    parser = argparse.ArgumentParser(description='WSL截屏工具')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-d', '--delay', type=int, default=0, help='延迟秒数')
    
    args = parser.parse_args()
    
    print("🖼️  WSL截屏工具启动...")
    
    result = take_screenshot_wsl(args.delay, args.output)
    if result:
        print(f"🎉 截屏完成！文件保存在: {result}")
        print("💡 现在可以告诉Claude查看这个截屏文件")
    else:
        print("❌ 截屏失败")
        sys.exit(1)

if __name__ == "__main__":
    main()