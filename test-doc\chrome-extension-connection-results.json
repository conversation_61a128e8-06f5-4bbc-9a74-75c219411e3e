{"extension_found": false, "extension_enabled": false, "connection_working": false, "test_results": [{"timestamp": "2025-08-03T09:49:55.871326", "test_name": "Chrome配置文件发现", "success": true, "details": "找到配置文件: ['Profile 3', 'Profile 4']", "error": "", "data": null}, {"timestamp": "2025-08-03T09:49:56.039907", "test_name": "MCP扩展检查-Profile 3", "success": false, "details": "", "error": "未找到MCP相关扩展", "data": null}, {"timestamp": "2025-08-03T09:49:56.549113", "test_name": "MCP扩展检查-Profile 4", "success": false, "details": "", "error": "未找到MCP相关扩展", "data": null}, {"timestamp": "2025-08-03T09:49:59.437633", "test_name": "HTTP连接测试", "success": false, "details": "", "error": "HTTP状态码: 400", "data": null}, {"timestamp": "2025-08-03T09:50:00.685067", "test_name": "STDIO命令测试", "success": true, "details": "mcp-chrome-bridge命令可用", "error": "", "data": null}]}