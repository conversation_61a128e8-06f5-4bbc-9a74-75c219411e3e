#!/usr/bin/env python3
"""
MCP SSE连接测试脚本
测试Chrome MCP Server的Server-Sent Events连接
"""

import requests
import json
import uuid
import time
from datetime import datetime

class MCPSSEConnectionTest:
    def __init__(self):
        self.base_url = "http://127.0.0.1:12306"
        self.session_id = str(uuid.uuid4())
        self.results = []
        
    def log_result(self, test_name, success, details="", error=""):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error,
            "session_id": self.session_id
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def test_sse_connection(self):
        """测试SSE连接"""
        try:
            url = f"{self.base_url}/mcp"
            headers = {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
            
            # 尝试建立SSE连接
            response = requests.get(url, headers=headers, stream=True, timeout=10)
            
            if response.status_code == 200:
                self.log_result("SSE连接测试", True, f"成功建立SSE连接")
                return True
            else:
                self.log_result("SSE连接测试", False, f"状态码: {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_result("SSE连接测试", False, error=str(e))
            return False
    
    def test_mcp_with_session(self):
        """使用会话ID测试MCP调用"""
        try:
            url = f"{self.base_url}/mcp"
            
            # 尝试不同的请求格式
            test_payloads = [
                # 标准MCP格式
                {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {},
                        "clientInfo": {
                            "name": "test-client",
                            "version": "1.0.0"
                        }
                    }
                },
                # 工具列表请求
                {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/list",
                    "params": {}
                }
            ]
            
            for i, payload in enumerate(test_payloads):
                headers = {
                    'Content-Type': 'application/json',
                    'X-Session-ID': self.session_id,
                    'Accept': 'application/json'
                }
                
                response = requests.post(url, json=payload, headers=headers, timeout=10)
                
                test_name = f"MCP调用测试 {i+1} ({'initialize' if i == 0 else 'tools/list'})"
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if "result" in data:
                            self.log_result(test_name, True, f"成功获得响应: {json.dumps(data, indent=2)[:200]}...")
                            return True
                        else:
                            self.log_result(test_name, False, f"响应格式异常: {data}")
                    except json.JSONDecodeError:
                        self.log_result(test_name, False, f"JSON解析失败: {response.text}")
                else:
                    self.log_result(test_name, False, f"状态码: {response.status_code}", response.text)
                
                time.sleep(1)  # 请求间隔
            
            return False
            
        except Exception as e:
            self.log_result("MCP会话测试", False, error=str(e))
            return False
    
    def test_direct_tool_call(self):
        """直接测试工具调用"""
        try:
            # 尝试直接调用get_windows_and_tabs工具
            url = f"{self.base_url}/mcp"
            payload = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "get_windows_and_tabs",
                    "arguments": {}
                }
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-Session-ID': self.session_id
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=15)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "result" in data and not data.get("error"):
                        self.log_result("直接工具调用测试", True, f"成功调用工具: {json.dumps(data, indent=2)[:300]}...")
                        return True
                    else:
                        error_msg = data.get("error", {}).get("message", "未知错误")
                        self.log_result("直接工具调用测试", False, error=error_msg)
                        return False
                except json.JSONDecodeError:
                    self.log_result("直接工具调用测试", False, f"JSON解析失败: {response.text}")
                    return False
            else:
                self.log_result("直接工具调用测试", False, f"状态码: {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_result("直接工具调用测试", False, error=str(e))
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🔍 MCP SSE连接测试")
        print(f"会话ID: {self.session_id}")
        print("=" * 60)
        
        tests = [
            self.test_sse_connection,
            self.test_mcp_with_session,
            self.test_direct_tool_call
        ]
        
        success_count = 0
        for test in tests:
            if test():
                success_count += 1
            time.sleep(2)  # 测试间隔
        
        print("=" * 60)
        print(f"📊 测试完成: {success_count}/{len(tests)} 项测试通过")
        
        return success_count > 0
    
    def save_results(self, filename="test-doc/mcp-sse-connection-results.json"):
        """保存测试结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"📄 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    tester = MCPSSEConnectionTest()
    
    print("MCP SSE连接测试工具")
    print("=" * 60)
    
    success = tester.run_all_tests()
    tester.save_results()
    
    if success:
        print("\n🎉 至少一项测试成功！MCP连接可能已建立")
        print("可以继续进行功能测试")
    else:
        print("\n❌ 所有测试失败")
        print("建议检查Chrome MCP扩展是否正确配置")
    
    return 0 if success else 1

if __name__ == "__main__":
    main()
