[{"timestamp": "2025-08-03T09:38:09.623386", "test_name": "Chrome进程检查", "success": true, "details": "发现 11 个Chrome进程", "error": ""}, {"timestamp": "2025-08-03T09:38:10.627981", "test_name": "MCP Bridge安装检查", "success": false, "details": "mcp-chrome-bridge命令未找到", "error": ""}, {"timestamp": "2025-08-03T09:38:12.265196", "test_name": "Native Messaging注册检查", "success": true, "details": "发现Chrome相关的Native Messaging配置", "error": ""}, {"timestamp": "2025-08-03T09:38:15.015967", "test_name": "STDIO配置文件检查", "success": true, "details": "找到配置文件: C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js", "error": ""}, {"timestamp": "2025-08-03T09:38:16.020841", "test_name": "Chrome扩展检查", "success": true, "details": "需要手动验证扩展状态", "error": ""}]