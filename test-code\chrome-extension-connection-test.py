#!/usr/bin/env python3
"""
Chrome扩展连接状态测试脚本
验证已安装的Chrome MCP扩展连接状态
"""

import json
import subprocess
import time
import os
from datetime import datetime

class ChromeExtensionConnectionTest:
    def __init__(self):
        self.results = []
        self.extension_found = False
        self.extension_enabled = False
        self.connection_working = False
        
    def log_result(self, test_name, success, details="", error="", data=None):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error,
            "data": data
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def check_extension_in_all_profiles(self):
        """检查所有Chrome配置文件中的扩展"""
        chrome_user_data = "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
        
        if not os.path.exists(chrome_user_data):
            self.log_result("Chrome用户数据检查", False, error="Chrome用户数据目录不存在")
            return False
        
        # 查找所有配置文件
        profiles = []
        for item in os.listdir(chrome_user_data):
            item_path = os.path.join(chrome_user_data, item)
            if os.path.isdir(item_path) and (item == "Default" or item.startswith("Profile")):
                profiles.append(item)
        
        self.log_result("Chrome配置文件发现", True, f"找到配置文件: {profiles}")
        
        # 检查每个配置文件中的扩展
        for profile in profiles:
            extensions_dir = os.path.join(chrome_user_data, profile, "Extensions")
            if os.path.exists(extensions_dir):
                extensions = [d for d in os.listdir(extensions_dir) if os.path.isdir(os.path.join(extensions_dir, d))]
                
                # 查找可能的Chrome MCP扩展
                mcp_extensions = []
                for ext_id in extensions:
                    ext_path = os.path.join(extensions_dir, ext_id)
                    versions = [d for d in os.listdir(ext_path) if os.path.isdir(os.path.join(ext_path, d))]
                    
                    if versions:
                        latest_version = max(versions)
                        manifest_path = os.path.join(ext_path, latest_version, "manifest.json")
                        
                        if os.path.exists(manifest_path):
                            try:
                                with open(manifest_path, 'r', encoding='utf-8') as f:
                                    manifest = json.load(f)
                                
                                name = manifest.get("name", "").lower()
                                description = manifest.get("description", "").lower()
                                
                                # 检查是否是Chrome MCP相关扩展
                                if any(keyword in name or keyword in description for keyword in ["mcp", "chrome mcp", "browser bridge", "native messaging"]):
                                    mcp_extensions.append({
                                        "id": ext_id,
                                        "name": manifest.get("name", "未知"),
                                        "version": manifest.get("version", "未知"),
                                        "profile": profile
                                    })
                                    self.extension_found = True
                            except Exception as e:
                                continue
                
                if mcp_extensions:
                    for ext in mcp_extensions:
                        self.log_result(f"MCP扩展发现-{ext['profile']}", True, 
                                      f"ID: {ext['id']}, 名称: {ext['name']}, 版本: {ext['version']}")
                else:
                    self.log_result(f"MCP扩展检查-{profile}", False, error="未找到MCP相关扩展")
        
        return self.extension_found
    
    def check_extension_preferences(self):
        """检查扩展启用状态"""
        chrome_user_data = "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
        profiles = ["Default", "Profile 1", "Profile 2"]
        
        for profile in profiles:
            prefs_path = os.path.join(chrome_user_data, profile, "Preferences")
            if os.path.exists(prefs_path):
                try:
                    with open(prefs_path, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)
                    
                    extensions = prefs.get("extensions", {}).get("settings", {})
                    enabled_extensions = []
                    
                    for ext_id, settings in extensions.items():
                        if settings.get("state", 0) == 1:  # 1 = enabled
                            enabled_extensions.append(ext_id)
                    
                    if enabled_extensions:
                        self.log_result(f"启用扩展检查-{profile}", True, 
                                      f"启用的扩展数: {len(enabled_extensions)}")
                        self.extension_enabled = True
                    else:
                        self.log_result(f"启用扩展检查-{profile}", False, error="无启用的扩展")
                        
                except Exception as e:
                    self.log_result(f"扩展偏好检查-{profile}", False, error=str(e))
    
    def test_http_connection(self):
        """测试HTTP连接"""
        try:
            import requests
            
            # 测试HTTP端点
            response = requests.get("http://127.0.0.1:12306/mcp", timeout=5)
            
            if response.status_code == 200:
                self.log_result("HTTP连接测试", True, "HTTP端点响应正常")
                self.connection_working = True
            elif response.status_code == 404:
                self.log_result("HTTP连接测试", False, error="HTTP端点返回404，可能需要会话ID")
            else:
                self.log_result("HTTP连接测试", False, error=f"HTTP状态码: {response.status_code}")
                
        except ImportError:
            self.log_result("HTTP连接测试", False, error="requests库未安装，跳过HTTP测试")
        except Exception as e:
            self.log_result("HTTP连接测试", False, error=str(e))
    
    def test_stdio_connection_simple(self):
        """简单测试STDIO连接"""
        try:
            # 测试mcp-chrome-bridge命令是否可用
            result = subprocess.run([
                "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\mcp-chrome-bridge.cmd", "--help"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.log_result("STDIO命令测试", True, "mcp-chrome-bridge命令可用")
            else:
                self.log_result("STDIO命令测试", False, error="mcp-chrome-bridge命令失败")
                
        except Exception as e:
            self.log_result("STDIO命令测试", False, error=str(e))
    
    def provide_connection_guide(self):
        """提供连接指南"""
        print("\n🔧 Chrome MCP Server 连接配置指南")
        print("=" * 60)
        
        if not self.extension_found:
            print("❌ 扩展未找到或未正确安装")
            print("解决方案:")
            print("1. 确认已从GitHub下载最新扩展")
            print("2. 在Chrome中访问 chrome://extensions/")
            print("3. 启用'开发者模式'")
            print("4. 点击'加载已解压的扩展程序'")
            print("5. 选择扩展文件夹")
            print()
        
        if self.extension_found and not self.extension_enabled:
            print("⚠️ 扩展已安装但可能未启用")
            print("解决方案:")
            print("1. 访问 chrome://extensions/")
            print("2. 找到Chrome MCP Server扩展")
            print("3. 确保开关为蓝色（启用状态）")
            print()
        
        if self.extension_found and self.extension_enabled:
            print("✅ 扩展状态正常")
            print("下一步:")
            print("1. 点击Chrome中的扩展图标")
            print("2. 确认显示'Connected'状态")
            print("3. 如果显示'Disconnected'，点击'Connect'按钮")
            print()
        
        print("🔄 推荐的Augment配置:")
        print("方案1 - HTTP连接（推荐）:")
        print(json.dumps({
            "mcpServers": {
                "chrome-mcp-server": {
                    "type": "streamableHttp",
                    "url": "http://127.0.0.1:12306/mcp"
                }
            }
        }, indent=2))
        
        print("\n方案2 - STDIO连接:")
        stdio_path = "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"
        print(json.dumps({
            "mcpServers": {
                "chrome-mcp-stdio": {
                    "command": "node",
                    "args": [stdio_path]
                }
            }
        }, indent=2))
    
    def run_connection_test(self):
        """运行连接测试"""
        print("🔍 Chrome MCP Server 连接状态测试")
        print("=" * 60)
        
        tests = [
            self.check_extension_in_all_profiles,
            self.check_extension_preferences,
            self.test_http_connection,
            self.test_stdio_connection_simple
        ]
        
        for test in tests:
            try:
                test()
                time.sleep(1)
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        self.provide_connection_guide()
    
    def save_results(self, filename="test-doc/chrome-extension-connection-results.json"):
        """保存测试结果"""
        try:
            summary = {
                "extension_found": self.extension_found,
                "extension_enabled": self.extension_enabled,
                "connection_working": self.connection_working,
                "test_results": self.results
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            print(f"\n📄 连接测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    tester = ChromeExtensionConnectionTest()
    
    try:
        tester.run_connection_test()
        tester.save_results()
        
        if tester.extension_found and tester.extension_enabled:
            print("\n🎉 扩展状态良好，请按照上述配置指南设置Augment")
        else:
            print("\n⚠️ 发现扩展问题，请按照上述指南解决")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    main()
