{"chrome_mcp_server_configs": {"description": "Chrome MCP Server 配置模板 - 适用于各种AI编程工具", "version": "1.0", "test_status": "✅ 100% 功能测试通过", "deployment_ready": true, "last_updated": "2025-08-03", "stdio_connection_recommended": {"description": "STDIO连接方式 - 推荐使用，已验证100%功能正常", "test_status": "✅ 已通过完整功能测试 (23/23工具)", "advantages": ["兼容性最好", "适用于所有支持MCP的AI工具", "不依赖HTTP端口", "已验证稳定可靠"], "config": {"mcpServers": {"chrome-mcp-stdio": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"]}}}, "requirements": ["Chrome浏览器正在运行", "Chrome MCP扩展已安装并连接", "端口12306未被占用"]}, "http_connection_alternative": {"description": "HTTP连接方式 - 备选方案，目前状态不稳定", "test_status": "⚠️ 当前返回503错误，需要排查", "advantages": ["理论上响应更快", "支持流式传输", "适用于支持HTTP的客户端"], "config": {"mcpServers": {"chrome-mcp-server": {"type": "streamableHttp", "url": "http://127.0.0.1:12306/mcp"}}}, "path_variations": {"npm_global": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js", "pnpm_global": "C:\\Users\\<USER>\\AppData\\Roaming\\pnpm\\global\\5\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js", "note": "请根据实际安装位置调整路径"}, "requirements": ["mcp-chrome-bridge已全局安装", "Native Messaging已注册", "Chrome MCP扩展已安装"]}, "augment_specific_config": {"description": "Augment Code 专用配置", "config_file_location": "~/.augment/config.json", "http_config": {"mcpServers": {"chrome-mcp-server": {"type": "streamableHttp", "url": "http://127.0.0.1:12306/mcp", "description": "Chrome浏览器自动化工具 - HTTP连接"}}}, "stdio_config": {"mcpServers": {"chrome-mcp-stdio": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"], "description": "Chrome浏览器自动化工具 - STDIO连接"}}}}, "claude_desktop_config": {"description": "<PERSON> 配置", "config_file_location": "%APPDATA%\\Claude\\claude_desktop_config.json", "http_config": {"mcpServers": {"chrome-mcp-server": {"type": "streamableHttp", "url": "http://127.0.0.1:12306/mcp"}}}, "stdio_config": {"mcpServers": {"chrome-mcp-stdio": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"]}}}}, "cline_vscode_config": {"description": "Cline (VSCode扩展) 配置", "config_file_location": "VSCode设置 > Cline > MCP Servers", "http_config": {"mcpServers": {"chrome-mcp-server": {"type": "streamableHttp", "url": "http://127.0.0.1:12306/mcp"}}}, "stdio_config": {"mcpServers": {"chrome-mcp-stdio": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"]}}}}, "installation_verification": {"description": "安装验证步骤", "steps": [{"step": 1, "action": "检查Node.js版本", "command": "node --version", "expected": ">= 18.19.0"}, {"step": 2, "action": "检查mcp-chrome-bridge安装", "command": "mcp-chrome-bridge --version", "expected": "显示版本号"}, {"step": 3, "action": "验证Native Messaging注册", "command": "mcp-chrome-bridge register --force", "expected": "Successfully registered"}, {"step": 4, "action": "检查Chrome扩展状态", "location": "chrome://extensions/", "expected": "Chrome MCP Server扩展已启用"}, {"step": 5, "action": "验证扩展连接", "location": "点击扩展图标", "expected": "显示'Connected'状态"}]}, "troubleshooting": {"description": "常见问题解决方案", "issues": [{"problem": "Failed to connect to MCP server", "causes": ["Chrome扩展未启用", "扩展显示Disconnected状态", "Chrome浏览器未运行"], "solutions": ["检查chrome://extensions/中的扩展状态", "点击扩展图标，确认Connected状态", "重启Chrome浏览器"]}, {"problem": "mcp-chrome-bridge command not found", "causes": ["未全局安装mcp-chrome-bridge", "PATH环境变量问题"], "solutions": ["npm install -g mcp-chrome-bridge", "重新注册: mcp-chrome-bridge register --force"]}, {"problem": "STDIO路径错误", "causes": ["安装路径与配置不匹配", "使用了错误的包管理器路径"], "solutions": ["使用 npm list -g mcp-chrome-bridge 查找路径", "更新配置文件中的args路径", "重启AI编程工具"]}, {"problem": "HTTP端点无响应", "causes": ["端口12306被占用", "Chrome扩展HTTP服务未启动"], "solutions": ["重新加载Chrome扩展", "检查端口占用: netstat -an | findstr 12306", "重启Chrome浏览器"]}]}, "supported_tools": {"description": "支持的23个工具列表", "categories": {"browser_management": ["get_windows_and_tabs", "chrome_navigate", "chrome_close_tabs", "chrome_go_back_or_forward", "chrome_inject_script", "chrome_send_command_to_inject_script"], "screenshot": ["chrome_screenshot"], "interaction": ["chrome_get_interactive_elements", "chrome_click_element", "chrome_fill_or_select", "chrome_keyboard"], "network_monitoring": ["chrome_network_capture_start", "chrome_network_capture_stop", "chrome_network_debugger_start", "chrome_network_debugger_stop", "chrome_network_request"], "content_analysis": ["chrome_get_web_content", "search_tabs_content", "chrome_console"], "data_management": ["chrome_history", "chrome_bookmark_search", "chrome_bookmark_add", "chrome_bookmark_delete"]}, "total_tools": 23, "test_status": "100% 功能验证通过"}}}