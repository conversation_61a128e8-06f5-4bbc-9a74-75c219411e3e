#!/usr/bin/env python3
"""
Chrome扩展状态检查脚本
检查Chrome MCP扩展是否正确安装和运行
"""

import requests
import json
import subprocess
import time
from datetime import datetime

class ChromeExtensionChecker:
    def __init__(self):
        self.results = []
        
    def log_result(self, check_name, success, details="", suggestion=""):
        """记录检查结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "check_name": check_name,
            "success": success,
            "details": details,
            "suggestion": suggestion
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {check_name}")
        if details:
            print(f"   详情: {details}")
        if suggestion:
            print(f"   建议: {suggestion}")
        print()
    
    def check_chrome_processes(self):
        """检查Chrome进程"""
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-Process chrome -ErrorAction SilentlyContinue | Measure-Object | Select-Object Count"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # 解析输出获取进程数量
                output = result.stdout.strip()
                if "Count" in output:
                    lines = output.split('\n')
                    for line in lines:
                        if line.strip().isdigit():
                            count = int(line.strip())
                            if count > 0:
                                self.log_result("Chrome进程检查", True, f"发现 {count} 个Chrome进程")
                                return True
                            else:
                                self.log_result("Chrome进程检查", False, "未发现Chrome进程", "请启动Chrome浏览器")
                                return False
                
                self.log_result("Chrome进程检查", True, "Chrome正在运行")
                return True
            else:
                self.log_result("Chrome进程检查", False, "无法检查Chrome进程", "请确认PowerShell可用")
                return False
                
        except Exception as e:
            self.log_result("Chrome进程检查", False, f"检查失败: {str(e)}")
            return False
    
    def check_port_12306(self):
        """检查端口12306是否被占用"""
        try:
            result = subprocess.run(
                ["powershell", "-Command", "netstat -an | Select-String ':12306'"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                self.log_result("端口12306检查", True, "端口12306正在被使用")
                return True
            else:
                self.log_result("端口12306检查", False, "端口12306未被占用", "MCP server可能未启动")
                return False
                
        except Exception as e:
            self.log_result("端口12306检查", False, f"检查失败: {str(e)}")
            return False
    
    def check_mcp_server_response(self):
        """检查MCP server响应"""
        try:
            # 尝试连接到MCP server
            response = requests.get("http://127.0.0.1:12306", timeout=5)
            
            if response.status_code == 200:
                self.log_result("MCP Server响应检查", True, f"服务器响应正常 (状态码: {response.status_code})")
                return True
            elif response.status_code == 503:
                self.log_result("MCP Server响应检查", False, 
                              "服务器返回503 - 服务不可用", 
                              "Chrome扩展可能未连接或配置错误")
                return False
            else:
                self.log_result("MCP Server响应检查", False, f"异常状态码: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            self.log_result("MCP Server响应检查", False, "无法连接到服务器", "MCP server可能未启动")
            return False
        except Exception as e:
            self.log_result("MCP Server响应检查", False, f"检查失败: {str(e)}")
            return False
    
    def check_chrome_extensions_page(self):
        """提示检查Chrome扩展页面"""
        print("🔍 手动检查Chrome扩展:")
        print("1. 在Chrome中访问: chrome://extensions/")
        print("2. 确认'Chrome MCP Server'扩展已安装并启用")
        print("3. 点击扩展图标，确认显示'Connected'状态")
        print("4. 如果显示'Disconnected'，点击'Connect'按钮")
        print()
        
        self.log_result("Chrome扩展手动检查", True, "需要手动验证", "请按照上述步骤检查")
        return True
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🔍 Chrome MCP扩展状态检查")
        print("=" * 50)
        
        checks = [
            self.check_chrome_processes,
            self.check_port_12306,
            self.check_mcp_server_response,
            self.check_chrome_extensions_page
        ]
        
        success_count = 0
        for check in checks:
            if check():
                success_count += 1
            time.sleep(1)
        
        print("=" * 50)
        print(f"📊 检查完成: {success_count-1}/{len(checks)-1} 项自动检查通过")  # 减去手动检查项
        
        return success_count >= 3  # 至少3项通过（包括手动检查）
    
    def save_results(self, filename="test-doc/chrome-extension-check-results.json"):
        """保存检查结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"📄 检查结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    checker = ChromeExtensionChecker()
    
    print("Chrome MCP扩展状态检查工具")
    print("=" * 50)
    
    success = checker.run_all_checks()
    checker.save_results()
    
    if not success:
        print("\n🔧 故障排除步骤:")
        print("1. 重新安装Chrome MCP扩展")
        print("2. 检查扩展权限设置")
        print("3. 重启Chrome浏览器")
        print("4. 检查防火墙和安全软件设置")
        print("5. 尝试使用不同的端口")
    
    return 0 if success else 1

if __name__ == "__main__":
    main()
