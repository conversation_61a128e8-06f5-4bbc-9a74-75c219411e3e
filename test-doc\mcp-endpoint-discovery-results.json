[{"timestamp": "2025-08-03T09:33:34.450883", "endpoint": "/", "status_code": 404, "response_text": "{\"message\":\"Route GET:/ not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.456083", "endpoint": "/mcp", "status_code": 400, "response_text": "{\"error\":\"Invalid or missing MCP session ID for SSE.\"}", "success": false}, {"timestamp": "2025-08-03T09:33:34.471352", "endpoint": "/api", "status_code": 404, "response_text": "{\"message\":\"Route GET:/api not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.490824", "endpoint": "/api/mcp", "status_code": 404, "response_text": "{\"message\":\"Route GET:/api/mcp not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.502666", "endpoint": "/chrome-mcp", "status_code": 404, "response_text": "{\"message\":\"Route GET:/chrome-mcp not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.512869", "endpoint": "/health", "status_code": 404, "response_text": "{\"message\":\"Route GET:/health not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.531007", "endpoint": "/status", "status_code": 404, "response_text": "{\"message\":\"Route GET:/status not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.542475", "endpoint": "/ping", "status_code": 404, "response_text": "{\"message\":\"Route GET:/ping not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.561582", "endpoint": "/ (MCP调用)", "status_code": 404, "response_text": "{\"message\":\"Route POST:/ not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.578109", "endpoint": "/mcp (MCP调用)", "status_code": 400, "response_text": "{\"error\":\"Invalid MCP request or session.\"}", "success": false}, {"timestamp": "2025-08-03T09:33:34.599170", "endpoint": "/api (MCP调用)", "status_code": 404, "response_text": "{\"message\":\"Route POST:/api not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.605746", "endpoint": "/api/mcp (MCP调用)", "status_code": 404, "response_text": "{\"message\":\"Route POST:/api/mcp not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}, {"timestamp": "2025-08-03T09:33:34.616108", "endpoint": "/chrome-mcp (MCP调用)", "status_code": 404, "response_text": "{\"message\":\"Route POST:/chrome-mcp not found\",\"error\":\"Not Found\",\"statusCode\":404}", "success": false}]