#!/usr/bin/env python3
"""
MCP端点发现脚本
尝试发现正确的MCP端点路径
"""

import requests
import json
from datetime import datetime

class MCPEndpointDiscovery:
    def __init__(self):
        self.base_url = "http://127.0.0.1:12306"
        self.results = []
        
    def log_result(self, endpoint, status_code, response_text="", success=False):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "endpoint": endpoint,
            "status_code": status_code,
            "response_text": response_text[:200] if response_text else "",  # 限制长度
            "success": success
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {endpoint} - 状态码: {status_code}")
        if response_text and len(response_text) < 100:
            print(f"   响应: {response_text}")
        print()
    
    def test_endpoint(self, path):
        """测试单个端点"""
        url = f"{self.base_url}{path}"
        try:
            response = requests.get(url, timeout=5)
            success = response.status_code == 200
            self.log_result(path, response.status_code, response.text, success)
            return success, response
        except Exception as e:
            self.log_result(path, 0, str(e), False)
            return False, None
    
    def test_mcp_call(self, path):
        """测试MCP调用"""
        url = f"{self.base_url}{path}"
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        
        try:
            response = requests.post(
                url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            success = response.status_code == 200 and "result" in response.json()
            response_text = json.dumps(response.json(), indent=2) if response.status_code == 200 else response.text
            self.log_result(f"{path} (MCP调用)", response.status_code, response_text, success)
            return success, response
        except Exception as e:
            self.log_result(f"{path} (MCP调用)", 0, str(e), False)
            return False, None
    
    def discover_endpoints(self):
        """发现可用的端点"""
        print("🔍 MCP端点发现")
        print("=" * 50)
        
        # 常见的端点路径
        endpoints_to_test = [
            "/",
            "/mcp",
            "/api",
            "/api/mcp",
            "/chrome-mcp",
            "/health",
            "/status",
            "/ping"
        ]
        
        print("📡 测试GET请求端点:")
        working_endpoints = []
        for endpoint in endpoints_to_test:
            success, response = self.test_endpoint(endpoint)
            if success:
                working_endpoints.append(endpoint)
        
        print("\n📡 测试MCP调用端点:")
        mcp_endpoints = ["/", "/mcp", "/api", "/api/mcp", "/chrome-mcp"]
        working_mcp_endpoints = []
        for endpoint in mcp_endpoints:
            success, response = self.test_mcp_call(endpoint)
            if success:
                working_mcp_endpoints.append(endpoint)
        
        print("=" * 50)
        print(f"📊 发现结果:")
        print(f"   可用GET端点: {len(working_endpoints)} 个")
        print(f"   可用MCP端点: {len(working_mcp_endpoints)} 个")
        
        if working_mcp_endpoints:
            print(f"\n🎉 找到可用的MCP端点:")
            for endpoint in working_mcp_endpoints:
                print(f"   {self.base_url}{endpoint}")
        else:
            print(f"\n⚠️  未找到可用的MCP端点")
            if working_endpoints:
                print(f"   但找到以下可用端点:")
                for endpoint in working_endpoints:
                    print(f"   {self.base_url}{endpoint}")
        
        return working_mcp_endpoints
    
    def save_results(self, filename="test-doc/mcp-endpoint-discovery-results.json"):
        """保存发现结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"\n📄 发现结果已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存结果失败: {e}")

def main():
    """主函数"""
    discovery = MCPEndpointDiscovery()
    
    print("MCP端点发现工具")
    print("=" * 50)
    
    working_endpoints = discovery.discover_endpoints()
    discovery.save_results()
    
    if working_endpoints:
        print(f"\n✅ 成功！可以使用以下端点进行MCP通信:")
        for endpoint in working_endpoints:
            print(f"   http://127.0.0.1:12306{endpoint}")
    else:
        print(f"\n❌ 未找到可用的MCP端点")
        print(f"   建议检查Chrome MCP扩展配置")
    
    return 0 if working_endpoints else 1

if __name__ == "__main__":
    main()
