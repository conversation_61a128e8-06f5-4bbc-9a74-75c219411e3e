#!/usr/bin/env python3
"""
Chrome扩展详细状态检查脚本
检查Chrome MCP扩展的安装和连接状态
"""

import json
import subprocess
import time
import os
from datetime import datetime

class ChromeExtensionDetailedCheck:
    def __init__(self):
        self.results = []
        self.chrome_user_data = None
        self.extension_id = "hbdgbgagpkpjffpklnamcljpakneikee"  # 从注册信息中获取的扩展ID
        
    def log_result(self, test_name, success, details="", error="", data=None):
        """记录检查结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error,
            "data": data
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def find_chrome_user_data(self):
        """查找Chrome用户数据目录"""
        possible_paths = [
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data"),
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome Beta\\User Data"),
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome Dev\\User Data"),
            "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.chrome_user_data = path
                self.log_result("Chrome用户数据目录", True, f"找到目录: {path}")
                return True
        
        self.log_result("Chrome用户数据目录", False, error="未找到Chrome用户数据目录")
        return False
    
    def check_extension_installation(self):
        """检查扩展安装状态"""
        if not self.chrome_user_data:
            return False
        
        # 检查Default配置文件中的扩展
        profiles = ["Default", "Profile 1", "Profile 2"]
        found_extension = False
        
        for profile in profiles:
            extensions_path = os.path.join(self.chrome_user_data, profile, "Extensions", self.extension_id)
            if os.path.exists(extensions_path):
                # 查找版本目录
                try:
                    versions = [d for d in os.listdir(extensions_path) if os.path.isdir(os.path.join(extensions_path, d))]
                    if versions:
                        latest_version = max(versions)
                        manifest_path = os.path.join(extensions_path, latest_version, "manifest.json")
                        
                        if os.path.exists(manifest_path):
                            with open(manifest_path, 'r', encoding='utf-8') as f:
                                manifest = json.load(f)
                            
                            extension_name = manifest.get("name", "未知")
                            extension_version = manifest.get("version", "未知")
                            
                            self.log_result(f"扩展安装检查-{profile}", True, 
                                          f"扩展名: {extension_name}, 版本: {extension_version}",
                                          data={"profile": profile, "version": extension_version, "manifest": manifest})
                            found_extension = True
                        else:
                            self.log_result(f"扩展安装检查-{profile}", False, error="manifest.json不存在")
                    else:
                        self.log_result(f"扩展安装检查-{profile}", False, error="未找到版本目录")
                except Exception as e:
                    self.log_result(f"扩展安装检查-{profile}", False, error=str(e))
        
        if not found_extension:
            self.log_result("扩展安装检查", False, error="未在任何配置文件中找到扩展")
        
        return found_extension
    
    def check_extension_preferences(self):
        """检查扩展偏好设置"""
        if not self.chrome_user_data:
            return False
        
        profiles = ["Default", "Profile 1", "Profile 2"]
        
        for profile in profiles:
            prefs_path = os.path.join(self.chrome_user_data, profile, "Preferences")
            if os.path.exists(prefs_path):
                try:
                    with open(prefs_path, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)
                    
                    # 检查扩展设置
                    extensions = prefs.get("extensions", {}).get("settings", {})
                    if self.extension_id in extensions:
                        ext_settings = extensions[self.extension_id]
                        state = ext_settings.get("state", 0)  # 1=enabled, 0=disabled
                        was_installed = ext_settings.get("was_installed_by_default", False)
                        
                        state_text = "启用" if state == 1 else "禁用"
                        self.log_result(f"扩展偏好设置-{profile}", True,
                                      f"状态: {state_text}, 默认安装: {was_installed}",
                                      data={"profile": profile, "settings": ext_settings})
                    else:
                        self.log_result(f"扩展偏好设置-{profile}", False, error="扩展设置不存在")
                        
                except Exception as e:
                    self.log_result(f"扩展偏好设置-{profile}", False, error=str(e))
    
    def check_native_messaging_config(self):
        """检查Native Messaging配置"""
        # 检查注册表配置
        try:
            result = subprocess.run([
                "powershell", "-Command",
                f"Get-ItemProperty -Path 'HKCU:\\Software\\Google\\Chrome\\NativeMessagingHosts\\com.chromemcp.nativehost' -ErrorAction SilentlyContinue"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                self.log_result("Native Messaging注册表", True, "配置存在")
                
                # 检查配置文件
                config_path = "C:\\Users\\<USER>\\AppData\\Roaming\\Google\\Chrome\\NativeMessagingHosts\\com.chromemcp.nativehost.json"
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    self.log_result("Native Messaging配置文件", True,
                                  f"路径: {config.get('path', '未知')}",
                                  data=config)
                else:
                    self.log_result("Native Messaging配置文件", False, error="配置文件不存在")
            else:
                self.log_result("Native Messaging注册表", False, error="注册表配置不存在")
                
        except Exception as e:
            self.log_result("Native Messaging配置检查", False, error=str(e))
    
    def check_chrome_processes_detailed(self):
        """详细检查Chrome进程"""
        try:
            result = subprocess.run([
                "powershell", "-Command",
                "Get-Process chrome -ErrorAction SilentlyContinue | Select-Object Id, ProcessName, MainWindowTitle, StartTime | ConvertTo-Json"
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    processes = json.loads(result.stdout)
                    if not isinstance(processes, list):
                        processes = [processes]
                    
                    process_count = len(processes)
                    main_windows = [p for p in processes if p.get("MainWindowTitle")]
                    
                    self.log_result("Chrome进程详细检查", True,
                                  f"总进程数: {process_count}, 主窗口数: {len(main_windows)}",
                                  data={"processes": processes[:5]})  # 只保存前5个进程信息
                except json.JSONDecodeError:
                    self.log_result("Chrome进程详细检查", True, "Chrome正在运行（解析详情失败）")
            else:
                self.log_result("Chrome进程详细检查", False, error="Chrome未运行")
                
        except Exception as e:
            self.log_result("Chrome进程详细检查", False, error=str(e))
    
    def check_mcp_bridge_status(self):
        """检查MCP Bridge状态"""
        try:
            # 检查安装状态
            result = subprocess.run([
                "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\mcp-chrome-bridge.cmd", "status"
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                self.log_result("MCP Bridge状态", True, "状态检查成功", data={"output": result.stdout})
            else:
                self.log_result("MCP Bridge状态", False, error=result.stderr)
                
        except Exception as e:
            self.log_result("MCP Bridge状态", False, error=str(e))
    
    def generate_troubleshooting_guide(self):
        """生成故障排除指南"""
        print("\n🔧 Chrome MCP Server 连接问题排除指南")
        print("=" * 60)
        
        # 分析结果并提供建议
        extension_installed = any(r["success"] for r in self.results if "扩展安装检查" in r["test_name"])
        extension_enabled = any(r["success"] and "启用" in r.get("details", "") for r in self.results if "扩展偏好设置" in r["test_name"])
        native_messaging_ok = any(r["success"] for r in self.results if "Native Messaging" in r["test_name"])
        chrome_running = any(r["success"] for r in self.results if "Chrome进程" in r["test_name"])
        
        print(f"📊 诊断结果:")
        print(f"  Chrome运行: {'✅' if chrome_running else '❌'}")
        print(f"  扩展安装: {'✅' if extension_installed else '❌'}")
        print(f"  扩展启用: {'✅' if extension_enabled else '❌'}")
        print(f"  Native Messaging: {'✅' if native_messaging_ok else '❌'}")
        print()
        
        if not chrome_running:
            print("🚨 Chrome未运行 - 请启动Chrome浏览器")
        
        if not extension_installed:
            print("🚨 扩展未安装 - 需要安装Chrome MCP Server扩展")
            print("   1. 访问Chrome Web Store")
            print("   2. 搜索'Chrome MCP Server'或使用扩展ID: hbdgbgagpkpjffpklnamcljpakneikee")
            print("   3. 点击'添加到Chrome'")
        
        if extension_installed and not extension_enabled:
            print("🚨 扩展已安装但未启用")
            print("   1. 访问 chrome://extensions/")
            print("   2. 找到Chrome MCP Server扩展")
            print("   3. 确保开关为蓝色（启用状态）")
        
        if not native_messaging_ok:
            print("🚨 Native Messaging配置问题")
            print("   1. 重新注册: mcp-chrome-bridge register --force")
            print("   2. 检查权限: 确保有管理员权限")
            print("   3. 重启Chrome浏览器")
        
        print("\n🔄 推荐的完整修复流程:")
        print("1. 完全关闭Chrome浏览器")
        print("2. 重新注册Native Messaging")
        print("3. 启动Chrome并检查扩展状态")
        print("4. 点击扩展图标确认连接状态")
        print("5. 重新测试MCP连接")
    
    def run_detailed_check(self):
        """运行详细检查"""
        print("🔍 Chrome MCP Server 扩展详细状态检查")
        print("=" * 60)
        
        checks = [
            self.find_chrome_user_data,
            self.check_chrome_processes_detailed,
            self.check_extension_installation,
            self.check_extension_preferences,
            self.check_native_messaging_config,
            self.check_mcp_bridge_status
        ]
        
        for check in checks:
            try:
                check()
                time.sleep(1)
            except Exception as e:
                print(f"❌ 检查失败: {e}")
        
        self.generate_troubleshooting_guide()
    
    def save_results(self, filename="test-doc/chrome-extension-detailed-results.json"):
        """保存检查结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细检查结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    checker = ChromeExtensionDetailedCheck()
    
    try:
        checker.run_detailed_check()
        checker.save_results()
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 检查被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    main()
