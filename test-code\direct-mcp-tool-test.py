#!/usr/bin/env python3
"""
直接MCP工具测试脚本
测试当前环境中已配置的Chrome MCP工具
"""

import json
import subprocess
import time
from datetime import datetime

class DirectMCPToolTest:
    def __init__(self):
        self.results = []
        
    def log_result(self, test_name, success, details="", error=""):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def test_chrome_processes(self):
        """检查Chrome进程"""
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-Process chrome -ErrorAction SilentlyContinue | Measure-Object | Select-Object Count"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout.strip()
                if "Count" in output:
                    lines = output.split('\n')
                    for line in lines:
                        if line.strip().isdigit():
                            count = int(line.strip())
                            if count > 0:
                                self.log_result("Chrome进程检查", True, f"发现 {count} 个Chrome进程")
                                return True
                
                self.log_result("Chrome进程检查", True, "Chrome正在运行")
                return True
            else:
                self.log_result("Chrome进程检查", False, "无法检查Chrome进程")
                return False
                
        except Exception as e:
            self.log_result("Chrome进程检查", False, error=str(e))
            return False
    
    def test_chrome_extension_check(self):
        """检查Chrome扩展状态"""
        print("🔍 Chrome扩展检查指南:")
        print("1. 打开Chrome浏览器")
        print("2. 访问 chrome://extensions/")
        print("3. 查找 'Chrome MCP Server' 或类似的扩展")
        print("4. 确认扩展已启用（开关为蓝色）")
        print("5. 点击扩展图标，查看连接状态")
        print()
        
        self.log_result("Chrome扩展检查", True, "需要手动验证扩展状态")
        return True
    
    def test_mcp_bridge_installation(self):
        """检查MCP Bridge安装"""
        try:
            result = subprocess.run(
                ["mcp-chrome-bridge", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log_result("MCP Bridge安装检查", True, f"版本: {version}")
                return True
            else:
                self.log_result("MCP Bridge安装检查", False, f"命令执行失败: {result.stderr}")
                return False
                
        except FileNotFoundError:
            self.log_result("MCP Bridge安装检查", False, "mcp-chrome-bridge命令未找到")
            return False
        except Exception as e:
            self.log_result("MCP Bridge安装检查", False, error=str(e))
            return False
    
    def test_native_messaging_registration(self):
        """检查Native Messaging注册"""
        try:
            # 检查注册表中的Native Messaging配置
            result = subprocess.run([
                "powershell", "-Command", 
                "Get-ItemProperty -Path 'HKCU:\\Software\\Google\\Chrome\\NativeMessagingHosts\\*' -ErrorAction SilentlyContinue | Select-Object PSChildName"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                hosts = result.stdout.strip()
                if "chrome" in hosts.lower() or "mcp" in hosts.lower():
                    self.log_result("Native Messaging注册检查", True, "发现Chrome相关的Native Messaging配置")
                    return True
                else:
                    self.log_result("Native Messaging注册检查", False, "未发现Chrome MCP相关配置")
                    return False
            else:
                self.log_result("Native Messaging注册检查", False, "无法检查注册表配置")
                return False
                
        except Exception as e:
            self.log_result("Native Messaging注册检查", False, error=str(e))
            return False
    
    def test_stdio_config_file(self):
        """检查stdio配置文件"""
        try:
            # 查找可能的配置文件位置
            config_paths = [
                "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js",
                "C:\\Users\\<USER>\\AppData\\Local\\pnpm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js",
                "stdio-config.json"
            ]
            
            found_configs = []
            for path in config_paths:
                try:
                    result = subprocess.run(
                        ["powershell", "-Command", f"Test-Path '{path}'"],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    if result.returncode == 0 and "True" in result.stdout:
                        found_configs.append(path)
                except:
                    continue
            
            if found_configs:
                self.log_result("STDIO配置文件检查", True, f"找到配置文件: {found_configs[0]}")
                return True
            else:
                self.log_result("STDIO配置文件检查", False, "未找到stdio配置文件")
                return False
                
        except Exception as e:
            self.log_result("STDIO配置文件检查", False, error=str(e))
            return False
    
    def provide_troubleshooting_guide(self):
        """提供故障排除指南"""
        print("\n🔧 Chrome MCP Server STDIO连接故障排除指南:")
        print("=" * 60)
        print("1. 确认Chrome浏览器正在运行")
        print("2. 确认Chrome MCP扩展已安装并启用")
        print("3. 重新注册Native Messaging:")
        print("   mcp-chrome-bridge register --force")
        print("4. 检查扩展连接状态:")
        print("   - 点击Chrome中的扩展图标")
        print("   - 确认显示'Connected'状态")
        print("5. 重启Chrome浏览器")
        print("6. 检查Augment配置中的MCP server路径")
        print("7. 尝试手动运行stdio服务器进行测试")
        print("=" * 60)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🔍 Chrome MCP Server 环境诊断")
        print("=" * 60)
        
        tests = [
            self.test_chrome_processes,
            self.test_mcp_bridge_installation,
            self.test_native_messaging_registration,
            self.test_stdio_config_file,
            self.test_chrome_extension_check
        ]
        
        success_count = 0
        for test in tests:
            if test():
                success_count += 1
            time.sleep(1)
        
        print("=" * 60)
        print(f"📊 诊断完成: {success_count}/{len(tests)} 项检查通过")
        
        if success_count < 3:
            self.provide_troubleshooting_guide()
        
        return success_count >= 3
    
    def save_results(self, filename="test-doc/direct-mcp-tool-test-results.json"):
        """保存测试结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"📄 诊断结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    tester = DirectMCPToolTest()
    
    print("Chrome MCP Server 环境诊断工具")
    print("=" * 60)
    
    success = tester.run_all_tests()
    tester.save_results()
    
    if success:
        print("\n✅ 环境诊断基本通过")
        print("如果仍然无法连接，请按照故障排除指南操作")
    else:
        print("\n❌ 环境诊断发现问题")
        print("请按照上述故障排除指南解决问题")
    
    return 0 if success else 1

if __name__ == "__main__":
    main()
