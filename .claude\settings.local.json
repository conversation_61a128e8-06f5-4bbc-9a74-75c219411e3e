{"permissions": {"allow": ["<PERSON><PERSON>(mkdir -p test-code test-doc)", "Bash(ls -la test-code/ test-doc/)", "Bash(find:*)", "Bash(ls:*)", "Bash(echo $PATH)", "Bash(node:*)", "Bash(npm:*)", "Bash(claude --version)", "Bash(if [ -f ~/.nvm/nvm.sh ])", "<PERSON><PERSON>(then echo \"NVM 已安装\")", "Bash(else echo \"NVM 未安装\")", "Bash(fi)", "Bash(cp:*)", "Bash(sudo apt remove:*)", "<PERSON><PERSON>(source:*)", "Bash(nvm list:*)", "Bash(export NVM_DIR=\"$HOME/.nvm\")", "Bash([ -s \"$NVM_DIR/nvm.sh\" ])", "Bash(. \"$NVM_DIR/nvm.sh\")", "Bash(bash:*)", "<PERSON><PERSON>(chmod:*)", "Bash(nvm use:*)", "<PERSON><PERSON>(env)", "Bash(grep:*)", "Bash(nvm:*)", "Bash(~/.npm-global/bin/claude:*)", "Bash(sudo rm:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(whereis:*)", "Bash(echo $SHELL)", "<PERSON><PERSON>(printenv)", "<PERSON><PERSON>(cat:*)", "Bash(BACKUP_DIR=~/backup-wsl-cleanup)", "<PERSON><PERSON>(echo:*)", "Bash(sudo npm list -g @anthropic-ai/claude-code)", "Bash(sudo npm uninstall -g @anthropic-ai/claude-code)", "Bash(powershell.exe:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(/mnt/d/Programs/Python/Python311/python.exe --version)", "mcp__mcp-feedback-collector__collect_feedback", "mcp__mcp-feedback-collector__pick_image", "<PERSON><PERSON>(diff:*)", "Bash(/mnt/d/Programs/Python/Python311/python.exe:*)", "<PERSON><PERSON>(true)", "Bash(claude /mcp)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude config list)", "<PERSON><PERSON>(claude:*)", "<PERSON><PERSON>(code:*)", "<PERSON><PERSON>(alias code:*)", "Bash('/mnt/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/code' --version)", "Bash('/mnt/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/code' --install-extension ms-vscode-remote.remote-wsl)", "Bash('/mnt/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/code' --install-extension anthropic.claude-code)", "Bash('/mnt/c/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/code' --list-extensions)", "Bash(export:*)", "Bash(sqlite3:*)", "Bash(./fix-vscode-terminal-path.sh:*)", "Bash(hash -r)", "Bash(beep)", "Bash(ps:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(md5sum:*)", "Bash(find /mnt/c/Users -name \"settings.json\" -path \"*/Code/User/*\")", "Bash(ls -la \"$HOME/.vscode-server/data/User/\")", "Bash(echo \"当前目录：$(pwd)\")", "Bash(echo \"1. 当前WSL目录：$(pwd)\")", "Bash(echo \"1. 检查当前路径：$(pwd)\")", "Bash(ls -la *.code-workspace)", "Bash(ls -la .vscode/settings.json)", "Bash(mount)", "<PERSON><PERSON>(mv:*)", "Bash(cmd.exe /c \"chcp & echo. & echo %LANG% & echo %LC_ALL%\")", "mcp__mcp-feedback-collector__collect_feedback", "Bash(grep:*)", "Bash(source ~/.bashrc)", "Bash(find:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(where.exe:*)", "Bash(pnpm:*)", "<PERSON><PERSON>(go:*)", "<PERSON><PERSON>(curl:*)", "Bash(PYTHONPATH=\"/mnt/d/Programs/Python/Python311/Lib/site-packages\" /mnt/d/Programs/Python/Python311/python.exe -m mcp_feedback_collector.server --help)", "WebFetch(domain:github.com)", "WebFetch(domain:browsermcp.io)", "Bash(npx @browser-mcp/mcp-server --help)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(check_config_version)", "Bash(/tmp/test_claude_config.sh:*)", "<PERSON><PERSON>(dos2unix:*)", "Bash(npx:*)", "Bash(/mnt/g/z-vscode-claudecode/config-test/validate-global-mcp.sh)", "Bash(~/.claude/backup-settings.sh:*)", "<PERSON><PERSON>(git clone:*)", "<PERSON><PERSON>(time curl:*)", "Bash(/home/<USER>/.claude/backup-settings.sh:*)", "Bash(jq . /mnt/g/z-vscode-claudecode/.claude/settings.json)", "Bash(/usr/bin/python3:*)", "Bash(/dev/null)", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "<PERSON><PERSON>(timeout:*)", "Bash(awk:*)", "Bash(git config:*)"], "deny": []}, "enabledMcpjsonServers": ["mcp-feedback-collector", "context7"]}