[{"timestamp": "2025-08-03T09:32:53.642314", "check_name": "Chrome进程检查", "success": true, "details": "发现 15 个Chrome进程", "suggestion": ""}, {"timestamp": "2025-08-03T09:32:55.351357", "check_name": "端口12306检查", "success": true, "details": "端口12306正在被使用", "suggestion": ""}, {"timestamp": "2025-08-03T09:32:56.370292", "check_name": "MCP Server响应检查", "success": false, "details": "异常状态码: 404", "suggestion": ""}, {"timestamp": "2025-08-03T09:32:57.371167", "check_name": "Chrome扩展手动检查", "success": true, "details": "需要手动验证", "suggestion": "请按照上述步骤检查"}]