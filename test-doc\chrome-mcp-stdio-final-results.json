[{"timestamp": "2025-08-03T09:39:17.769987", "test_name": "STDIO服务器启动", "success": true, "details": "进程ID: 20768", "error": "", "response_data": null}, {"timestamp": "2025-08-03T09:39:20.777504", "test_name": "初始化测试", "success": true, "details": "MCP服务器初始化成功", "error": "", "response_data": {"protocolVersion": "2024-11-05", "capabilities": {"tools": {}}, "serverInfo": {"name": "StdioChromeMcpServer", "version": "1.0.0"}}}, {"timestamp": "2025-08-03T09:39:22.784766", "test_name": "工具列表测试", "success": true, "details": "发现 23 个工具，前10个: ['get_windows_and_tabs', 'chrome_navigate', 'chrome_screenshot', 'chrome_close_tabs', 'chrome_go_back_or_forward', 'chrome_get_web_content', 'chrome_click_element', 'chrome_fill_or_select', 'chrome_get_interactive_elements', 'chrome_network_request']", "error": "", "response_data": {"tool_count": 23, "sample_tools": ["get_windows_and_tabs", "chrome_navigate", "chrome_screenshot", "chrome_close_tabs", "chrome_go_back_or_forward", "chrome_get_web_content", "chrome_click_element", "chrome_fill_or_select", "chrome_get_interactive_elements", "chrome_network_request"]}}, {"timestamp": "2025-08-03T09:39:24.863208", "test_name": "简单工具调用测试", "success": false, "details": "", "error": "响应数据格式错误", "response_data": null}]