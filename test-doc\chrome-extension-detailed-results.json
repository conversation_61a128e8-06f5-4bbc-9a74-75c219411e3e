[{"timestamp": "2025-08-03T09:47:54.504505", "test_name": "Chrome用户数据目录", "success": true, "details": "找到目录: C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data", "error": "", "data": null}, {"timestamp": "2025-08-03T09:47:56.474626", "test_name": "Chrome进程详细检查", "success": true, "details": "Chrome正在运行（解析详情失败）", "error": "", "data": null}, {"timestamp": "2025-08-03T09:47:57.476664", "test_name": "扩展安装检查", "success": false, "details": "", "error": "未在任何配置文件中找到扩展", "data": null}, {"timestamp": "2025-08-03T09:48:00.094040", "test_name": "Native Messaging注册表", "success": true, "details": "配置存在", "error": "", "data": null}, {"timestamp": "2025-08-03T09:48:00.094040", "test_name": "Native Messaging配置文件", "success": true, "details": "路径: C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\run_host.bat", "error": "", "data": {"name": "com.chromemcp.nativehost", "description": "Node.js Host for Browser Bridge Extension", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\run_host.bat", "type": "stdio", "allowed_origins": ["chrome-extension://hbdgbgagpkpjffpklnamcljpakneikee/"]}}, {"timestamp": "2025-08-03T09:48:01.289053", "test_name": "MCP Bridge状态", "success": false, "details": "", "error": "error: unknown command 'status'\n", "data": null}]