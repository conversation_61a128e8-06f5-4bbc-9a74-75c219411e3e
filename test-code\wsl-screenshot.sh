#!/bin/bash
# WSL截屏工具 - 专用于WSL环境，通过调用Windows PowerShell实现截屏
# 文件名: wsl-screenshot.sh
# 使用方法: ./wsl-screenshot.sh [延迟秒数] [输出文件名]
# 环境要求: WSL环境，能够调用Windows PowerShell

set -e

# 默认参数
DELAY=${1:-0}
OUTPUT=${2:-""}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🖼️  WSL截屏工具启动...${NC}"

# 检查是否在WSL环境中
if [[ ! -f /proc/version ]] || ! grep -qi microsoft /proc/version; then
    echo -e "${RED}❌ 此脚本需要在WSL环境中运行${NC}"
    exit 1
fi

# 构建PowerShell命令
PS_SCRIPT_PATH=$(wslpath -w "$SCRIPT_DIR/pwsh-screenshot.ps1")
echo -e "${YELLOW}📍 PowerShell脚本路径: $PS_SCRIPT_PATH${NC}"

# 确保Test-Image目录存在
mkdir -p "$SCRIPT_DIR/Test-Image"

# 构建参数
PS_ARGS=""
if [[ $DELAY -gt 0 ]]; then
    PS_ARGS="$PS_ARGS -Delay $DELAY"
    echo -e "${YELLOW}⏰ 设置延迟: ${DELAY}秒${NC}"
fi

if [[ -n "$OUTPUT" ]]; then
    # 转换为Windows路径，确保保存到Test-Image目录
    if [[ "$OUTPUT" == /* ]]; then
        # 绝对路径
        WIN_OUTPUT=$(wslpath -w "$OUTPUT")
    else
        # 相对路径保存到Test-Image目录
        WIN_OUTPUT=$(wslpath -w "$SCRIPT_DIR/Test-Image/$OUTPUT")
    fi
    PS_ARGS="$PS_ARGS -Output '$WIN_OUTPUT'"
    echo -e "${YELLOW}💾 输出文件: $WIN_OUTPUT${NC}"
else
    echo -e "${YELLOW}💾 输出目录: Test-Image/${NC}"
fi

# 执行PowerShell命令
echo -e "${GREEN}🚀 正在执行截屏...${NC}"
CMD="powershell.exe -ExecutionPolicy Bypass -File '$PS_SCRIPT_PATH' $PS_ARGS"
echo -e "${BLUE}🔧 执行命令: $CMD${NC}"

if eval "$CMD"; then
    echo -e "${GREEN}✅ 截屏成功完成！文件已保存到Test-Image目录${NC}"
    echo -e "${YELLOW}💡 提示: 现在可以告诉Claude查看截屏文件进行分析${NC}"
    
    # 列出Test-Image目录中最新的截屏文件
    echo -e "${BLUE}📁 Test-Image目录中的最新截屏文件:${NC}"
    find "$SCRIPT_DIR/Test-Image" -name "screenshot_*.png" -newermt "1 minute ago" 2>/dev/null | head -5 || \
    find "$SCRIPT_DIR/Test-Image" -name "screenshot_*.png" 2>/dev/null | tail -5
else
    echo -e "${RED}❌ 截屏失败，请检查PowerShell脚本是否存在${NC}"
    echo -e "${YELLOW}💡 尝试解决方案:${NC}"
    echo -e "   1. 确保 screenshot.ps1 文件存在"
    echo -e "   2. 检查WSL是否能访问Windows程序"
    echo -e "   3. 运行: powershell.exe -Command 'Get-ExecutionPolicy'"
    exit 1
fi