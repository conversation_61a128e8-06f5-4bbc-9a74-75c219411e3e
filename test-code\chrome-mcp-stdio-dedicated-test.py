#!/usr/bin/env python3
"""
Chrome MCP Server STDIO连接专项测试
验证纯STDIO连接方式的可用性
"""

import json
import subprocess
import time
import os
from datetime import datetime

class ChromeMCPStdioTest:
    def __init__(self):
        self.results = []
        self.stdio_working = False
        self.http_working = False
        
    def log_result(self, test_name, success, details="", error="", data=None):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error,
            "data": data
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def test_stdio_direct_connection(self):
        """直接测试STDIO连接"""
        print("🔌 测试STDIO直接连接")
        print("=" * 50)
        
        # 查找mcp-chrome-bridge路径
        possible_paths = [
            "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js",
            "C:\\Users\\<USER>\\AppData\\Roaming\\pnpm\\global\\5\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js",
            "C:\\Program Files\\nodejs\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"
        ]
        
        stdio_path = None
        for path in possible_paths:
            if os.path.exists(path):
                stdio_path = path
                break
        
        if not stdio_path:
            self.log_result("STDIO路径查找", False, error="未找到mcp-server-stdio.js文件")
            return False
        
        self.log_result("STDIO路径查找", True, f"找到路径: {stdio_path}")
        
        try:
            # 启动STDIO服务器
            process = subprocess.Popen([
                "node", stdio_path
            ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 发送MCP初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "roots": {"listChanged": True},
                        "sampling": {}
                    },
                    "clientInfo": {
                        "name": "chrome-mcp-test",
                        "version": "1.0.0"
                    }
                }
            }
            
            # 发送请求
            request_str = json.dumps(init_request) + "\n"
            process.stdin.write(request_str)
            process.stdin.flush()
            
            # 等待响应
            time.sleep(2)
            
            # 读取响应
            if process.poll() is None:  # 进程仍在运行
                try:
                    # 尝试读取输出
                    output = process.stdout.readline()
                    if output:
                        response = json.loads(output.strip())
                        if "result" in response:
                            self.log_result("STDIO初始化", True, "MCP协议初始化成功")
                            self.stdio_working = True
                            
                            # 发送工具列表请求
                            tools_request = {
                                "jsonrpc": "2.0",
                                "id": 2,
                                "method": "tools/list"
                            }
                            
                            tools_str = json.dumps(tools_request) + "\n"
                            process.stdin.write(tools_str)
                            process.stdin.flush()
                            
                            time.sleep(1)
                            tools_output = process.stdout.readline()
                            if tools_output:
                                tools_response = json.loads(tools_output.strip())
                                if "result" in tools_response and "tools" in tools_response["result"]:
                                    tool_count = len(tools_response["result"]["tools"])
                                    self.log_result("STDIO工具列表", True, f"发现{tool_count}个工具")
                                else:
                                    self.log_result("STDIO工具列表", False, error="工具列表响应格式错误")
                            else:
                                self.log_result("STDIO工具列表", False, error="无工具列表响应")
                        else:
                            self.log_result("STDIO初始化", False, error="初始化响应格式错误")
                    else:
                        self.log_result("STDIO初始化", False, error="无初始化响应")
                except json.JSONDecodeError as e:
                    self.log_result("STDIO初始化", False, error=f"JSON解析错误: {e}")
                except Exception as e:
                    self.log_result("STDIO初始化", False, error=f"读取响应错误: {e}")
            else:
                # 进程已退出，检查错误
                stderr_output = process.stderr.read()
                self.log_result("STDIO初始化", False, error=f"进程退出: {stderr_output}")
            
            # 清理进程
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()
                
        except Exception as e:
            self.log_result("STDIO连接测试", False, error=str(e))
            return False
        
        return self.stdio_working
    
    def test_http_connection(self):
        """测试HTTP连接"""
        print("🌐 测试HTTP连接")
        print("=" * 50)
        
        try:
            import requests
            
            # 测试基本连接
            response = requests.get("http://127.0.0.1:12306/mcp", timeout=5)
            
            if response.status_code == 200:
                self.log_result("HTTP基本连接", True, "HTTP端点响应正常")
                self.http_working = True
            elif response.status_code == 404:
                # 尝试SSE连接
                sse_response = requests.get("http://127.0.0.1:12306/sse", timeout=5)
                if sse_response.status_code == 200:
                    self.log_result("HTTP SSE连接", True, "SSE端点可用")
                    self.http_working = True
                else:
                    self.log_result("HTTP连接", False, error="HTTP和SSE端点都不可用")
            else:
                self.log_result("HTTP连接", False, error=f"HTTP状态码: {response.status_code}")
                
        except ImportError:
            self.log_result("HTTP连接测试", False, error="requests库未安装")
        except Exception as e:
            self.log_result("HTTP连接测试", False, error=str(e))
    
    def generate_connection_recommendations(self):
        """生成连接方式建议"""
        print("\n📋 连接方式测试总结")
        print("=" * 60)
        
        print(f"STDIO连接状态: {'✅ 可用' if self.stdio_working else '❌ 不可用'}")
        print(f"HTTP连接状态: {'✅ 可用' if self.http_working else '❌ 不可用'}")
        print()
        
        if self.stdio_working and self.http_working:
            print("🎉 两种连接方式都可用！")
            print("推荐策略:")
            print("- 优先使用HTTP连接（更快、更稳定）")
            print("- STDIO作为备选方案（兼容性更好）")
            
        elif self.stdio_working and not self.http_working:
            print("⚠️ 仅STDIO连接可用")
            print("建议:")
            print("- 使用STDIO连接配置")
            print("- 检查Chrome扩展HTTP服务状态")
            
        elif not self.stdio_working and self.http_working:
            print("⚠️ 仅HTTP连接可用")
            print("建议:")
            print("- 使用HTTP连接配置")
            print("- 检查mcp-chrome-bridge安装")
            
        else:
            print("❌ 两种连接方式都不可用")
            print("需要:")
            print("- 重新安装mcp-chrome-bridge")
            print("- 重新注册Native Messaging")
            print("- 检查Chrome扩展状态")
        
        print("\n🔧 针对只支持STDIO的AI工具:")
        if self.stdio_working:
            print("✅ 可以正常使用STDIO配置")
            print("配置示例:")
            stdio_config = {
                "mcpServers": {
                    "chrome-mcp-stdio": {
                        "command": "node",
                        "args": [
                            "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"
                        ]
                    }
                }
            }
            print(json.dumps(stdio_config, indent=2, ensure_ascii=False))
        else:
            print("❌ 需要先修复STDIO连接问题")
            print("修复步骤:")
            print("1. npm install -g mcp-chrome-bridge")
            print("2. mcp-chrome-bridge register --force")
            print("3. 重启Chrome浏览器")
    
    def run_comprehensive_test(self):
        """运行全面连接测试"""
        print("🔍 Chrome MCP Server 连接方式专项测试")
        print("=" * 70)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # 执行测试
        self.test_stdio_direct_connection()
        time.sleep(2)
        self.test_http_connection()
        
        # 生成建议
        self.generate_connection_recommendations()
    
    def save_results(self, filename="test-doc/chrome-mcp-stdio-dedicated-results.json"):
        """保存测试结果"""
        try:
            summary = {
                "stdio_working": self.stdio_working,
                "http_working": self.http_working,
                "test_results": self.results,
                "recommendations": {
                    "primary_connection": "http" if self.http_working else "stdio" if self.stdio_working else "none",
                    "stdio_only_support": self.stdio_working,
                    "both_available": self.stdio_working and self.http_working
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            print(f"\n📄 连接测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    tester = ChromeMCPStdioTest()
    
    try:
        tester.run_comprehensive_test()
        tester.save_results()
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    main()
