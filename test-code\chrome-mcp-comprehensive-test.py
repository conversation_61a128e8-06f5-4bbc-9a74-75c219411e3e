#!/usr/bin/env python3
"""
Chrome MCP Server 全面功能测试套件
测试所有23个工具的功能
"""

import json
import time
import os
from datetime import datetime

class ChromeMCPComprehensiveTest:
    def __init__(self):
        self.results = []
        self.test_summary = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "categories": {}
        }
        
    def log_result(self, category, test_name, success, details="", error="", response_data=None):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "category": category,
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error,
            "response_data": response_data
        }
        self.results.append(result)
        
        # 更新统计
        self.test_summary["total_tests"] += 1
        if success:
            self.test_summary["passed_tests"] += 1
        else:
            self.test_summary["failed_tests"] += 1
            
        if category not in self.test_summary["categories"]:
            self.test_summary["categories"][category] = {"passed": 0, "failed": 0}
        
        if success:
            self.test_summary["categories"][category]["passed"] += 1
        else:
            self.test_summary["categories"][category]["failed"] += 1
        
        status = "✅" if success else "❌"
        print(f"{status} [{category}] {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        print()
    
    def test_browser_management(self):
        """测试浏览器管理功能"""
        print("🌐 测试浏览器管理功能")
        print("=" * 50)
        
        # 测试1: 获取窗口和标签页
        try:
            from chrome_mcp_stdio import get_windows_and_tabs_chrome_mcp_stdio
            result = get_windows_and_tabs_chrome_mcp_stdio()
            if result and not result.get("error"):
                self.log_result("浏览器管理", "获取窗口和标签页", True, "成功获取浏览器信息")
            else:
                self.log_result("浏览器管理", "获取窗口和标签页", False, error="获取失败")
        except Exception as e:
            self.log_result("浏览器管理", "获取窗口和标签页", False, error=str(e))
        
        # 测试2: 导航到测试页面
        try:
            from chrome_mcp_stdio import chrome_navigate_chrome_mcp_stdio
            result = chrome_navigate_chrome_mcp_stdio(url="https://www.baidu.com")
            if result and not result.get("error"):
                self.log_result("浏览器管理", "页面导航", True, "成功导航到百度首页")
                time.sleep(3)  # 等待页面加载
            else:
                self.log_result("浏览器管理", "页面导航", False, error="导航失败")
        except Exception as e:
            self.log_result("浏览器管理", "页面导航", False, error=str(e))
        
        # 测试3: 获取页面内容
        try:
            from chrome_mcp_stdio import chrome_get_web_content_chrome_mcp_stdio
            result = chrome_get_web_content_chrome_mcp_stdio(textContent=True)
            if result and not result.get("error"):
                content = result.get("content", [])
                if content:
                    text_length = len(content[0].get("text", ""))
                    self.log_result("浏览器管理", "获取页面内容", True, f"成功获取内容，长度: {text_length}")
                else:
                    self.log_result("浏览器管理", "获取页面内容", False, error="内容为空")
            else:
                self.log_result("浏览器管理", "获取页面内容", False, error="获取内容失败")
        except Exception as e:
            self.log_result("浏览器管理", "获取页面内容", False, error=str(e))
    
    def test_screenshot_functionality(self):
        """测试截图功能"""
        print("📸 测试截图功能")
        print("=" * 50)
        
        try:
            from chrome_mcp_stdio import chrome_screenshot_chrome_mcp_stdio
            result = chrome_screenshot_chrome_mcp_stdio(
                fullPage=True,
                savePng=True,
                name="test-screenshot",
                storeBase64=False
            )
            if result and not result.get("error"):
                self.log_result("截图功能", "全页面截图", True, "成功生成截图")
            else:
                self.log_result("截图功能", "全页面截图", False, error="截图失败")
        except Exception as e:
            self.log_result("截图功能", "全页面截图", False, error=str(e))
    
    def test_interaction_features(self):
        """测试交互功能"""
        print("🎯 测试交互功能")
        print("=" * 50)
        
        # 测试1: 获取交互元素
        try:
            from chrome_mcp_stdio import chrome_get_interactive_elements_chrome_mcp_stdio
            result = chrome_get_interactive_elements_chrome_mcp_stdio(includeCoordinates=True)
            if result and not result.get("error"):
                content = result.get("content", [])
                if content:
                    elements_data = json.loads(content[0].get("text", "{}"))
                    element_count = len(elements_data.get("elements", []))
                    self.log_result("交互功能", "获取交互元素", True, f"发现 {element_count} 个交互元素")
                else:
                    self.log_result("交互功能", "获取交互元素", False, error="未找到交互元素")
            else:
                self.log_result("交互功能", "获取交互元素", False, error="获取交互元素失败")
        except Exception as e:
            self.log_result("交互功能", "获取交互元素", False, error=str(e))
        
        # 测试2: 搜索框交互（如果在百度页面）
        try:
            from chrome_mcp_stdio import chrome_fill_or_select_chrome_mcp_stdio
            result = chrome_fill_or_select_chrome_mcp_stdio(
                selector="input[name='wd']",
                value="Chrome MCP测试"
            )
            if result and not result.get("error"):
                self.log_result("交互功能", "填写搜索框", True, "成功填写搜索框")
            else:
                self.log_result("交互功能", "填写搜索框", False, error="填写失败")
        except Exception as e:
            self.log_result("交互功能", "填写搜索框", False, error=str(e))
    
    def test_network_monitoring(self):
        """测试网络监控功能"""
        print("🌐 测试网络监控功能")
        print("=" * 50)
        
        # 测试1: 启动网络捕获
        try:
            from chrome_mcp_stdio import chrome_network_capture_start_chrome_mcp_stdio
            result = chrome_network_capture_start_chrome_mcp_stdio()
            if result and not result.get("error"):
                self.log_result("网络监控", "启动网络捕获", True, "成功启动网络监控")
                
                # 等待一段时间收集网络请求
                time.sleep(3)
                
                # 停止网络捕获
                from chrome_mcp_stdio import chrome_network_capture_stop_chrome_mcp_stdio
                stop_result = chrome_network_capture_stop_chrome_mcp_stdio()
                if stop_result and not stop_result.get("error"):
                    content = stop_result.get("content", [])
                    if content:
                        requests_data = json.loads(content[0].get("text", "{}"))
                        request_count = len(requests_data.get("requests", []))
                        self.log_result("网络监控", "停止网络捕获", True, f"捕获到 {request_count} 个网络请求")
                    else:
                        self.log_result("网络监控", "停止网络捕获", True, "网络捕获完成，无请求数据")
                else:
                    self.log_result("网络监控", "停止网络捕获", False, error="停止捕获失败")
            else:
                self.log_result("网络监控", "启动网络捕获", False, error="启动网络监控失败")
        except Exception as e:
            self.log_result("网络监控", "网络监控", False, error=str(e))
    
    def test_data_management(self):
        """测试数据管理功能"""
        print("📚 测试数据管理功能")
        print("=" * 50)
        
        # 测试1: 搜索浏览历史
        try:
            from chrome_mcp_stdio import chrome_history_chrome_mcp_stdio
            result = chrome_history_chrome_mcp_stdio(
                maxResults=10,
                text="baidu"
            )
            if result and not result.get("error"):
                content = result.get("content", [])
                if content:
                    history_data = json.loads(content[0].get("text", "{}"))
                    history_count = len(history_data.get("history", []))
                    self.log_result("数据管理", "搜索浏览历史", True, f"找到 {history_count} 条历史记录")
                else:
                    self.log_result("数据管理", "搜索浏览历史", True, "历史搜索完成，无匹配记录")
            else:
                self.log_result("数据管理", "搜索浏览历史", False, error="搜索历史失败")
        except Exception as e:
            self.log_result("数据管理", "搜索浏览历史", False, error=str(e))
        
        # 测试2: 搜索书签
        try:
            from chrome_mcp_stdio import chrome_bookmark_search_chrome_mcp_stdio
            result = chrome_bookmark_search_chrome_mcp_stdio(
                query="google",
                maxResults=5
            )
            if result and not result.get("error"):
                content = result.get("content", [])
                if content:
                    bookmark_data = json.loads(content[0].get("text", "{}"))
                    bookmark_count = len(bookmark_data.get("bookmarks", []))
                    self.log_result("数据管理", "搜索书签", True, f"找到 {bookmark_count} 个书签")
                else:
                    self.log_result("数据管理", "搜索书签", True, "书签搜索完成，无匹配结果")
            else:
                self.log_result("数据管理", "搜索书签", False, error="搜索书签失败")
        except Exception as e:
            self.log_result("数据管理", "搜索书签", False, error=str(e))
    
    def test_advanced_features(self):
        """测试高级功能"""
        print("🚀 测试高级功能")
        print("=" * 50)
        
        # 测试1: 控制台输出捕获
        try:
            from chrome_mcp_stdio import chrome_console_chrome_mcp_stdio
            result = chrome_console_chrome_mcp_stdio(maxMessages=10)
            if result and not result.get("error"):
                content = result.get("content", [])
                if content:
                    console_data = json.loads(content[0].get("text", "{}"))
                    message_count = len(console_data.get("messages", []))
                    self.log_result("高级功能", "控制台输出捕获", True, f"捕获到 {message_count} 条控制台消息")
                else:
                    self.log_result("高级功能", "控制台输出捕获", True, "控制台捕获完成，无消息")
            else:
                self.log_result("高级功能", "控制台输出捕获", False, error="控制台捕获失败")
        except Exception as e:
            self.log_result("高级功能", "控制台输出捕获", False, error=str(e))
        
        # 测试2: 语义搜索
        try:
            from chrome_mcp_stdio import search_tabs_content_chrome_mcp_stdio
            result = search_tabs_content_chrome_mcp_stdio(query="搜索")
            if result and not result.get("error"):
                content = result.get("content", [])
                if content:
                    search_data = json.loads(content[0].get("text", "{}"))
                    result_count = len(search_data.get("results", []))
                    self.log_result("高级功能", "语义搜索", True, f"找到 {result_count} 个相关结果")
                else:
                    self.log_result("高级功能", "语义搜索", True, "语义搜索完成，无匹配结果")
            else:
                self.log_result("高级功能", "语义搜索", False, error="语义搜索失败")
        except Exception as e:
            self.log_result("高级功能", "语义搜索", False, error=str(e))
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🔍 Chrome MCP Server 全面功能测试")
        print("=" * 70)
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # 执行各类测试
        test_categories = [
            self.test_browser_management,
            self.test_screenshot_functionality,
            self.test_interaction_features,
            self.test_network_monitoring,
            self.test_data_management,
            self.test_advanced_features
        ]
        
        for test_category in test_categories:
            try:
                test_category()
                time.sleep(2)  # 测试间隔
            except Exception as e:
                print(f"❌ 测试类别执行失败: {e}")
        
        # 显示测试总结
        self.print_test_summary()
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 70)
        print("📊 测试总结报告")
        print("=" * 70)
        
        total = self.test_summary["total_tests"]
        passed = self.test_summary["passed_tests"]
        failed = self.test_summary["failed_tests"]
        success_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"总测试数: {total}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {failed}")
        print(f"成功率: {success_rate:.1f}%")
        print()
        
        print("分类测试结果:")
        for category, stats in self.test_summary["categories"].items():
            category_total = stats["passed"] + stats["failed"]
            category_rate = (stats["passed"] / category_total * 100) if category_total > 0 else 0
            print(f"  {category}: {stats['passed']}/{category_total} ({category_rate:.1f}%)")
        
        print("=" * 70)
        
        if success_rate >= 80:
            print("🎉 测试结果优秀！Chrome MCP Server功能基本正常")
        elif success_rate >= 60:
            print("✅ 测试结果良好！大部分功能正常工作")
        elif success_rate >= 40:
            print("⚠️ 测试结果一般，部分功能需要检查")
        else:
            print("❌ 测试结果较差，需要排查问题")
    
    def save_results(self, filename="test-doc/chrome-mcp-comprehensive-results.json"):
        """保存测试结果"""
        try:
            final_report = {
                "test_summary": self.test_summary,
                "detailed_results": self.results,
                "test_time": datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_report, f, ensure_ascii=False, indent=2)
            print(f"📄 详细测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    # 检查是否可以导入Chrome MCP工具
    try:
        import chrome_mcp_stdio
        print("✅ Chrome MCP工具导入成功")
    except ImportError:
        print("❌ 无法导入Chrome MCP工具，请检查配置")
        print("提示：确保在Augment环境中运行此测试")
        return 1
    
    tester = ChromeMCPComprehensiveTest()
    
    try:
        tester.run_comprehensive_test()
        tester.save_results()
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    main()
