{"stdio_working": true, "http_working": false, "test_results": [{"timestamp": "2025-08-03T10:04:49.178378", "test_name": "STDIO路径查找", "success": true, "details": "找到路径: C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js", "error": "", "data": null}, {"timestamp": "2025-08-03T10:04:51.195510", "test_name": "STDIO初始化", "success": true, "details": "MCP协议初始化成功", "error": "", "data": null}, {"timestamp": "2025-08-03T10:04:52.199742", "test_name": "STDIO工具列表", "success": true, "details": "发现23个工具", "error": "", "data": null}, {"timestamp": "2025-08-03T10:04:56.143763", "test_name": "HTTP连接", "success": false, "details": "", "error": "HTTP状态码: 503", "data": null}], "recommendations": {"primary_connection": "stdio", "stdio_only_support": true, "both_available": false}}