# PowerShell截屏工具 - 专用于PowerShell环境
# 文件名: pwsh-screenshot.ps1  
# 使用方法: ./pwsh-screenshot.ps1 [-Delay seconds] [-Output filename]
# 环境要求: Windows PowerShell 或 PowerShell Core

param(
    [int]$Delay = 0,
    [string]$Output = $null
)

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

# Ensure Test-Image directory exists
$testImageDir = Join-Path $scriptPath "Test-Image"
if (-not (Test-Path $testImageDir)) {
    New-Item -ItemType Directory -Path $testImageDir -Force | Out-Null
}

Write-Host "Taking screenshot..." -ForegroundColor Green

# Set output path to Test-Image directory
if (-not $Output) {
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $Output = Join-Path $testImageDir "screenshot_$timestamp.png"
} elseif (-not [System.IO.Path]::IsPathRooted($Output)) {
    # Relative paths also go to Test-Image directory
    $Output = Join-Path $testImageDir $Output
}

# Execute screenshot
if ($Delay -gt 0) {
    python test-code/screenshot_tool.py -d $Delay -o $Output
} else {
    python test-code/screenshot_tool.py -o $Output
}

Write-Host ""
Write-Host "Screenshot completed! File saved to Test-Image directory" -ForegroundColor Yellow
Write-Host "File path: $Output" -ForegroundColor Cyan
Write-Host "Tip: You can now ask Claude to view the latest screenshot" -ForegroundColor Cyan