#!/usr/bin/env python3
"""
Chrome MCP Server STDIO最终测试脚本
使用正确的路径测试stdio连接
"""

import json
import subprocess
import time
import threading
import queue
from datetime import datetime

class ChromeMCPStdioFinalTest:
    def __init__(self):
        self.results = []
        self.process = None
        self.request_id = 1
        self.stdio_path = "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"
        
    def log_result(self, test_name, success, details="", error="", response_data=None):
        """记录测试结果"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test_name": test_name,
            "success": success,
            "details": details,
            "error": error,
            "response_data": response_data
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        if error:
            print(f"   错误: {error}")
        if response_data and success:
            print(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)[:300]}...")
        print()
    
    def start_stdio_server(self):
        """启动stdio服务器"""
        try:
            # 使用node直接运行stdio服务器
            cmd = ["node", self.stdio_path]
            
            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )
            
            self.log_result("STDIO服务器启动", True, f"进程ID: {self.process.pid}")
            time.sleep(3)  # 等待服务器启动
            
            # 检查进程是否还在运行
            if self.process.poll() is None:
                return True
            else:
                stderr_output = self.process.stderr.read()
                self.log_result("STDIO服务器启动", False, error=f"进程退出: {stderr_output}")
                return False
            
        except FileNotFoundError:
            self.log_result("STDIO服务器启动", False, error="找不到node命令或stdio服务器文件")
            return False
        except Exception as e:
            self.log_result("STDIO服务器启动", False, error=str(e))
            return False
    
    def send_mcp_request(self, method, params=None, timeout=15):
        """发送MCP请求"""
        if not self.process or self.process.poll() is not None:
            return None, "STDIO服务器未运行"
        
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params or {}
        }
        self.request_id += 1
        
        try:
            # 发送请求
            request_json = json.dumps(request) + "\n"
            self.process.stdin.write(request_json)
            self.process.stdin.flush()
            
            # 读取响应
            start_time = time.time()
            while time.time() - start_time < timeout:
                if self.process.poll() is not None:
                    return None, "STDIO服务器进程已退出"
                
                try:
                    # 设置非阻塞读取
                    import select
                    import sys
                    
                    if sys.platform == "win32":
                        # Windows下使用不同的方法
                        line = self.process.stdout.readline()
                        if line.strip():
                            response = json.loads(line.strip())
                            return response, None
                    else:
                        # Unix系统
                        ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                        if ready:
                            line = self.process.stdout.readline()
                            if line.strip():
                                response = json.loads(line.strip())
                                return response, None
                    
                except json.JSONDecodeError as e:
                    continue
                except Exception as e:
                    return None, f"读取响应失败: {str(e)}"
                
                time.sleep(0.1)
            
            return None, "请求超时"
            
        except Exception as e:
            return None, f"发送请求失败: {str(e)}"
    
    def test_initialize(self):
        """测试初始化"""
        params = {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "chrome-mcp-test-client",
                "version": "1.0.0"
            }
        }
        
        response, error = self.send_mcp_request("initialize", params)
        
        if error:
            self.log_result("初始化测试", False, error=error)
            return False
        
        if response and "result" in response:
            self.log_result("初始化测试", True, "MCP服务器初始化成功", response_data=response["result"])
            return True
        else:
            self.log_result("初始化测试", False, error=f"初始化失败: {response}")
            return False
    
    def test_tools_list(self):
        """测试工具列表"""
        response, error = self.send_mcp_request("tools/list")
        
        if error:
            self.log_result("工具列表测试", False, error=error)
            return False
        
        if response and "result" in response:
            tools = response["result"].get("tools", [])
            tool_count = len(tools)
            tool_names = [tool.get("name", "未知") for tool in tools[:10]]  # 显示前10个工具名
            
            self.log_result("工具列表测试", True, 
                          f"发现 {tool_count} 个工具，前10个: {tool_names}", 
                          response_data={"tool_count": tool_count, "sample_tools": tool_names})
            return True
        else:
            self.log_result("工具列表测试", False, error=f"获取工具列表失败: {response}")
            return False
    
    def test_simple_tool_call(self):
        """测试简单工具调用"""
        params = {
            "name": "get_windows_and_tabs",
            "arguments": {}
        }
        
        response, error = self.send_mcp_request("tools/call", params)
        
        if error:
            self.log_result("简单工具调用测试", False, error=error)
            return False
        
        if response and "result" in response and not response.get("error"):
            content = response["result"].get("content", [])
            if content and len(content) > 0:
                text_content = content[0].get("text", "")
                try:
                    data = json.loads(text_content)
                    window_count = len(data.get("windows", []))
                    total_tabs = sum(len(window.get("tabs", [])) for window in data.get("windows", []))
                    
                    self.log_result("简单工具调用测试", True, 
                                  f"成功获取 {window_count} 个窗口，{total_tabs} 个标签页",
                                  response_data={"windows": window_count, "tabs": total_tabs})
                    return True
                except json.JSONDecodeError:
                    self.log_result("简单工具调用测试", False, error="响应数据格式错误")
                    return False
            else:
                self.log_result("简单工具调用测试", False, error="响应内容为空")
                return False
        else:
            error_msg = response.get("error", {}).get("message", "未知错误") if response else "无响应"
            self.log_result("简单工具调用测试", False, error=error_msg)
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
            self.process = None
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🔍 Chrome MCP Server STDIO最终测试")
        print(f"STDIO路径: {self.stdio_path}")
        print("=" * 70)
        
        # 启动STDIO服务器
        if not self.start_stdio_server():
            return False
        
        try:
            tests = [
                self.test_initialize,
                self.test_tools_list,
                self.test_simple_tool_call
            ]
            
            success_count = 0
            for test in tests:
                if test():
                    success_count += 1
                time.sleep(2)  # 测试间隔
            
            print("=" * 70)
            print(f"📊 测试完成: {success_count}/{len(tests)} 项测试通过")
            
            return success_count >= 2  # 至少2项测试通过认为连接正常
            
        finally:
            self.cleanup()
    
    def save_results(self, filename="test-doc/chrome-mcp-stdio-final-results.json"):
        """保存测试结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"📄 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    tester = ChromeMCPStdioFinalTest()
    
    print("Chrome MCP Server STDIO最终测试工具")
    print("=" * 70)
    
    try:
        success = tester.run_all_tests()
        tester.save_results()
        
        if success:
            print("\n🎉 STDIO连接测试成功！")
            print("Chrome MCP Server通过stdio方式工作正常")
            print("现在可以开始全面功能测试")
        else:
            print("\n❌ STDIO连接测试失败")
            print("请检查Chrome扩展是否正确连接")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        tester.cleanup()
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        tester.cleanup()
        return 1

if __name__ == "__main__":
    main()
